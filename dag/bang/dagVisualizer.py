import networkx as nx
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots
import pandas as pd
import numpy as np
from collections import defaultdict
import re

class DAGVisualizer:
    def __init__(self):
        self.G = nx.DiGraph()
        self.color_scheme = {
            'Primary': '#FF6B6B',
            'Biological_Process': '#4ECDC4', 
            'Neural': '#45B7D1',
            'Molecular': '#96CEB4',
            'Disease': '#D4A5A5',
            'Other': '#A9B7C0'
        }
        
    def parse_dag_string(self, dag_string):
        """Parse the DAG string to extract edges"""
        edges = []
        
        # Extract all edges using regex
        edge_pattern = r'(\w+(?:_\w+)*)\s*->\s*(\w+(?:_\w+)*)'
        matches = re.findall(edge_pattern, dag_string)
        
        for source, target in matches:
            edges.append((source, target))
            
        return edges
    
    def categorize_nodes(self, nodes):
        """Categorize nodes based on their names"""
        categories = {}
        
        primary_keywords = ['Hypertension', 'Alzheimers_Disease']
        biological_keywords = ['Inflammation', 'Oxidative_Stress', 'Cell_Death', 'Apoptosis', 'Necrosis']
        neural_keywords = ['Nerve_Degeneration', 'Memory_impairment', 'Cognitive', 'Dementia', 'Neural', 'Brain', 'Neuron']
        molecular_keywords = ['Amyloid', 'tau_Proteins', 'MAPT', 'APP_gene', 'APP_protein', 'Protein', 'Gene']
        disease_keywords = ['Cardiovascular_Diseases', 'Diabetes', 'Stroke', 'Cancer', 'Heart_failure', 'Kidney']
        
        for node in nodes:
            node_lower = node.lower()
            
            if any(keyword.lower() in node_lower for keyword in primary_keywords):
                categories[node] = 'Primary'
            elif any(keyword.lower() in node_lower for keyword in biological_keywords):
                categories[node] = 'Biological_Process'
            elif any(keyword.lower() in node_lower for keyword in neural_keywords):
                categories[node] = 'Neural'
            elif any(keyword.lower() in node_lower for keyword in molecular_keywords):
                categories[node] = 'Molecular'
            elif any(keyword.lower() in node_lower for keyword in disease_keywords):
                categories[node] = 'Disease'
            else:
                categories[node] = 'Other'
                
        return categories
    
    def build_graph(self, dag_string):
        """Build the NetworkX graph from DAG string"""
        edges = self.parse_dag_string(dag_string)
        
        # Add edges to graph
        self.G.add_edges_from(edges)
        
        # Get all nodes and categorize them
        nodes = list(self.G.nodes())
        node_categories = self.categorize_nodes(nodes)
        
        # Add node attributes
        nx.set_node_attributes(self.G, node_categories, 'category')
        
        # Add colors based on categories
        node_colors = {node: self.color_scheme[category] for node, category in node_categories.items()}
        nx.set_node_attributes(self.G, node_colors, 'color')
        
        return self.G
    
    def calculate_layout(self, layout_type='spring'):
        """Calculate node positions using different layout algorithms"""
        if layout_type == 'spring':
            pos = nx.spring_layout(self.G, k=3, iterations=50, seed=42)
        elif layout_type == 'circular':
            pos = nx.circular_layout(self.G)
        elif layout_type == 'kamada_kawai':
            pos = nx.kamada_kawai_layout(self.G)
        elif layout_type == 'hierarchical':
            pos = nx.nx_agraph.graphviz_layout(self.G, prog='dot')
        else:
            pos = nx.spring_layout(self.G, k=3, iterations=50, seed=42)
            
        return pos
    
    def create_interactive_plot(self, layout_type='spring', show_labels=True):
        """Create interactive Plotly visualization"""
        pos = self.calculate_layout(layout_type)
        
        # Extract node positions
        node_x = [pos[node][0] for node in self.G.nodes()]
        node_y = [pos[node][1] for node in self.G.nodes()]
        
        # Extract edge positions
        edge_x = []
        edge_y = []
        
        for edge in self.G.edges():
            x0, y0 = pos[edge[0]]
            x1, y1 = pos[edge[1]]
            edge_x.extend([x0, x1, None])
            edge_y.extend([y0, y1, None])
        
        # Create edge trace
        edge_trace = go.Scatter(
            x=edge_x, y=edge_y,
            line=dict(width=1, color='rgba(47, 79, 79, 0.5)'),
            hoverinfo='none',
            mode='lines',
            name='Connections'
        )
        
        # Create node traces for each category
        fig = go.Figure()
        
        # Add edges first
        fig.add_trace(edge_trace)
        
        # Group nodes by category
        categories = nx.get_node_attributes(self.G, 'category')
        category_groups = defaultdict(list)
        
        for node, category in categories.items():
            category_groups[category].append(node)
        
        # Add node traces for each category
        for category, nodes in category_groups.items():
            node_indices = [list(self.G.nodes()).index(node) for node in nodes]
            category_x = [node_x[i] for i in node_indices]
            category_y = [node_y[i] for i in node_indices]
            
            # Prepare hover text
            hover_text = []
            for node in nodes:
                # Get node statistics
                in_degree = self.G.in_degree(node)
                out_degree = self.G.out_degree(node)
                hover_text.append(f"{node.replace('_', ' ')}<br>"
                               f"Category: {category}<br>"
                               f"Incoming: {in_degree}<br>"
                               f"Outgoing: {out_degree}")
            
            node_trace = go.Scatter(
                x=category_x, y=category_y,
                mode='markers+text' if show_labels else 'markers',
                text=[node.replace('_', ' ') for node in nodes] if show_labels else None,
                textposition="middle center",
                textfont=dict(size=8, color='black'),
                hoverinfo='text',
                hovertext=hover_text,
                marker=dict(
                    size=15,
                    color=self.color_scheme[category],
                    line=dict(width=2, color='white'),
                    opacity=0.8
                ),
                name=category
            )
            
            fig.add_trace(node_trace)
        
        # Update layout
        fig.update_layout(
            title=dict(
                text="Alzheimer's Disease and Hypertension DAG Network",
                x=0.5,
                font=dict(size=20)
            ),
            showlegend=True,
            hovermode='closest',
            margin=dict(b=20,l=5,r=5,t=40),
            annotations=[ dict(
                text="Interactive network showing relationships between biological processes, diseases, and molecular factors",
                showarrow=False,
                xref="paper", yref="paper",
                x=0.005, y=-0.002,
                xanchor='left', yanchor='bottom',
                font=dict(color='gray', size=12)
            )],
            xaxis=dict(showgrid=False, zeroline=False, showticklabels=False),
            yaxis=dict(showgrid=False, zeroline=False, showticklabels=False),
            plot_bgcolor='white',
            width=1200,
            height=800
        )
        
        return fig
    
    def get_network_statistics(self):
        """Calculate and return network statistics"""
        stats = {
            'Total Nodes': len(self.G.nodes()),
            'Total Edges': len(self.G.edges()),
            'Is DAG': nx.is_directed_acyclic_graph(self.G),
            'Number of Components': nx.number_weakly_connected_components(self.G),
            'Average In-Degree': np.mean([d for n, d in self.G.in_degree()]),
            'Average Out-Degree': np.mean([d for n, d in self.G.out_degree()])
        }
        
        # Category distribution
        categories = nx.get_node_attributes(self.G, 'category')
        category_counts = pd.Series(list(categories.values())).value_counts()
        
        # Top nodes by degree
        degrees = dict(self.G.degree())
        top_nodes = sorted(degrees.items(), key=lambda x: x[1], reverse=True)[:10]
        
        return stats, category_counts, top_nodes
    
    def create_summary_dashboard(self):
        """Create a summary dashboard with statistics"""
        stats, category_counts, top_nodes = self.get_network_statistics()
        
        # Create subplots
        fig = make_subplots(
            rows=2, cols=2,
            subplot_titles=('Category Distribution', 'Top Nodes by Degree', 
                          'Network Statistics', 'Degree Distribution'),
            specs=[[{"type": "pie"}, {"type": "bar"}],
                   [{"type": "table"}, {"type": "histogram"}]]
        )
        
        # Category pie chart
        fig.add_trace(
            go.Pie(labels=category_counts.index, 
                   values=category_counts.values,
                   marker_colors=[self.color_scheme[cat] for cat in category_counts.index]),
            row=1, col=1
        )
        
        # Top nodes bar chart
        top_node_names = [node.replace('_', ' ')[:20] + '...' if len(node) > 20 
                         else node.replace('_', ' ') for node, _ in top_nodes]
        top_node_degrees = [degree for _, degree in top_nodes]
        
        fig.add_trace(
            go.Bar(x=top_node_degrees, y=top_node_names, orientation='h',
                   marker_color='lightblue'),
            row=1, col=2
        )
        
        # Statistics table
        stats_df = pd.DataFrame(list(stats.items()), columns=['Metric', 'Value'])
        fig.add_trace(
            go.Table(
                header=dict(values=['Metric', 'Value'], fill_color='lightgray'),
                cells=dict(values=[stats_df.Metric, stats_df.Value])
            ),
            row=2, col=1
        )
        
        # Degree distribution
        degrees = [d for n, d in self.G.degree()]
        fig.add_trace(
            go.Histogram(x=degrees, nbinsx=20, marker_color='lightgreen'),
            row=2, col=2
        )
        
        fig.update_layout(height=800, showlegend=False,
                         title_text="Network Analysis Dashboard")
        
        return fig

def main():
    # The DAG string from the R file
    dag_string = """
    Amputation -> Neoplasm
    Neoplasm -> Colorectal_Cancer
    1Methyl4phenyl1236tetrahydropyridine -> Nerve_Degeneration
    Energy_Balance -> Obesity
    Chlorpyrifos -> Oxidative_Stress
    Oxidopamine -> Oxidative_Stress
    Glucocorticoids -> Leucine_Zippers
    Oxidative_Stress -> Septicemia
    Angiotensin_II -> Inositol_Phosphates
    Anosognosia -> Alzheimers_Disease
    Cessation_of_life -> Pregnancy
    Inflammation -> Senile_Plaques
    Mutation -> Skeletal_dysplasia
    Epithelial_Cells -> Nitric_Oxide
    Mutation -> Protein_Structure
    Obstruction -> Growth
    Antineoplastic_Agents -> Neoplasms
    Transplantation -> Obesity
    Ethanol -> Liquid_diet
    Fibrosis -> Hypertensive_disease
    Angiotensin_II -> Mus
    Androgens -> Neoplasm
    Ethanol -> Fibrosis_Liver
    Inflammation -> Cell_Proliferation
    Dementia -> Family
    Ischemia -> Infarction
    TLR4_geneTLR4 -> Inflammation
    Carboplatin -> Neoplasm
    Dexamethasone -> Growth
    Ethanol -> Gastric_mucosa
    Body_Weight -> Obesity
    Ethanol -> alcohol_use_disorder
    Antibodies -> Inflammation
    Pancreaticoduodenectomy -> Neoplasm
    Neurodegenerative_Disorders -> Cessation_of_life
    Melatonin -> Oxidative_Stress
    Radiation_therapy -> Cessation_of_life
    Single_Nucleotide_Polymorphism -> Insulin_Resistance
    Toxic_effect -> Neoplasm
    Cyclosporine -> NEPHROTOXICITY
    Cerebral_Edema -> Cessation_of_life
    NADPH_Oxidase -> Oxidative_Stress
    Physical_activity -> Cerebrovascular_accident
    Cardiovascular_Diseases -> Chronic_Obstructive_Airway_Disease
    Minoxidil -> Hypertensive_disease
    Kidney_Transplantation -> End_stage_renal_failure
    Obesity -> insulin_sensitivity
    External_Beam_Radiation_Therapy -> Neoplasm
    Massive_hemorrhage -> Cessation_of_life
    Operative_Surgical_Procedures -> Conn_Syndrome
    Inflammation -> Liver_diseases
    Mutation -> Disorder_of_artery
    Endothelium -> Nitric_Oxide
    Squamous_cell_carcinoma -> Neoplasm
    Operative_Surgical_Procedures -> Cardiovascular_Diseases
    Inflammation -> TNF_protein_humanTNF
    Norepinephrine -> Dopamine
    Antioxidants -> Nitric_Oxide
    Insulin -> Hyperinsulinism
    Bariatric_Surgery -> Obesity
    darbepoetin_alfa -> Kidney_Failure_Chronic
    Malnutrition -> Cessation_of_life
    Hormone_replacement_therapy -> Cardiovascular_Diseases
    RNA_Small_Interfering -> Neoplasm
    Vas_Occlusion -> Ischemia
    Blood_Pressure -> Cardiovascular_Diseases
    Transcranial_Magnetic_Stimulation_Repetitive -> Alzheimers_Disease
    Cardiovascular_event -> Kidney_Failure_Chronic
    Sleep -> Sleep_Apnea_Obstructive
    Insulin_Resistance -> Fatty_Liver
    Leptin -> Obesity
    Norepinephrine -> depolarization
    Cyclosporine -> Nephrotic_Syndrome
    glucose_high -> Inflammation
    Apoptosis -> Nerve_Degeneration
    """
    
    # Create visualizer and build graph
    visualizer = DAGVisualizer()
    graph = visualizer.build_graph(dag_string)
    
    print("Graph successfully created!")
    print(f"Number of nodes: {len(graph.nodes())}")
    print(f"Number of edges: {len(graph.edges())}")
    
    # Create interactive visualization
    fig = visualizer.create_interactive_plot(layout_type='spring', show_labels=False)
    
    # Show the plot
    fig.show()
    
    # Create and show dashboard
    dashboard = visualizer.create_summary_dashboard()
    dashboard.show()
    
    # Print network statistics
    stats, category_counts, top_nodes = visualizer.get_network_statistics()
    
    print("\n=== NETWORK STATISTICS ===")
    for key, value in stats.items():
        print(f"{key}: {value}")
    
    print("\n=== CATEGORY DISTRIBUTION ===")
    print(category_counts)
    
    print("\n=== TOP 10 NODES BY DEGREE ===")
    for node, degree in top_nodes:
        print(f"{node}: {degree}")

if __name__ == "__main__":
    main()
