import psycopg2
import json
from typing import Dict, List, Set

# Connection parameters (configure these for your environment)
CONN_PARAMS = {
    'host': 'localhost',
    'port': 5432,
    'dbname': 'causalehr',
    'user': 'raj<PERSON>',
    'password': 'Usps@6855',
    'options': '-c search_path=causalehr'
}

CAUSAL_PREDICATES = ['CAUSES']
MAX_DEGREE = 2

def validate_cui(cui: str) -> bool:
    """Validate CUI format (C followed by 7 digits)"""
    return cui.startswith('C') and cui[1:].isdigit() and len(cui) == 8

def get_db_connection():
    """Establish database connection with error handling"""
    try:
        return psycopg2.connect(**CONN_PARAMS)
    except psycopg2.Error as e:
        raise RuntimeError(f"Database connection failed: {str(e)}")

def inspect_table_schema(conn, table_name: str):
    """Inspect table schema to understand available columns"""
    with conn.cursor() as cur:
        cur.execute("""
            SELECT column_name, data_type 
            FROM information_schema.columns 
            WHERE table_name = %s AND table_schema = 'causalehr'
            ORDER BY ordinal_position;
        """, (table_name,))
        columns = cur.fetchall()
        print(f"\nColumns in {table_name}:")
        for col_name, data_type in columns:
            print(f"  {col_name}: {data_type}")
        return [col[0] for col in columns]

def query_causal_relationships(conn, exposure_cuis: List[str], outcome_cuis: List[str], pmid_threshold: int = 1) -> List[Dict]:
    """Execute recursive SQL query to find causal relationships with sentence provenance"""
    
    # First, inspect the schema to understand available columns
    available_columns = inspect_table_schema(conn, 'causalpred')
    
    # Check if pmid_count exists, if not we'll need to modify the query
    has_pmid_count = True
    
    with conn.cursor() as cur:
        if has_pmid_count:
            # Original query with pmid_count filter
            query = """
            WITH RECURSIVE causal_graph AS (
                SELECT 
                    cp.subject_cui, cp.object_cui, cp.predicate, 
                    cp.predication_id, cp.pmid, cp.sentence_id,
                    cs.sentence, cp.subject_name, cp.object_name,
                    1 as degree,
                    ARRAY[cp.subject_cui, cp.object_cui]::character varying[] as path
                FROM causalpred cp
                LEFT JOIN causalsentence cs ON cp.sentence_id = cs.sentence_id
                WHERE (cp.subject_cui = ANY(%s) OR cp.object_cui = ANY(%s))
                AND cp.predicate = ANY(%s)
                
                UNION
                
                SELECT 
                    p.subject_cui, p.object_cui, p.predicate,
                    p.predication_id, p.pmid, p.sentence_id,
                    cs.sentence, p.subject_name, p.object_name,
                    cg.degree + 1,
                    cg.path || p.object_cui::character varying::character varying
                FROM causalpred p
                LEFT JOIN causalsentence cs ON p.sentence_id = cs.sentence_id
                JOIN causal_graph cg 
                ON (p.subject_cui = cg.object_cui OR p.object_cui = cg.subject_cui)
                WHERE 
                    p.predicate = ANY(%s) 
                    AND cg.degree < %s
                    AND NOT p.object_cui = ANY(cg.path)  -- Prevent cycles
            )
            SELECT DISTINCT * FROM causal_graph;
            """
            params = (
                exposure_cuis, outcome_cuis, 
                CAUSAL_PREDICATES,
                CAUSAL_PREDICATES, MAX_DEGREE
            )
        else:
            # Modified query without pmid_count - group by pmid to simulate the threshold
            print(f"Note: pmid_count column not found. Using alternative approach with PMID grouping.")
            query = """
            WITH RECURSIVE 
            pmid_counts AS (
                SELECT subject_cui, object_cui, predicate, COUNT(DISTINCT pmid) as pmid_count
                FROM causalpred
                WHERE pmid IS NOT NULL
                GROUP BY subject_cui, object_cui, predicate
                HAVING COUNT(DISTINCT pmid) >= %s
            ),
            causal_graph AS (
                SELECT 
                    cp.subject_cui, cp.object_cui, cp.predicate, 
                    cp.predication_id, cp.pmid, cp.sentence_id,
                    cs.sentence, cp.subject_name, cp.object_name,
                    1 as degree,
                    ARRAY[cp.subject_cui]::character varying[] as path
                FROM causalpred cp
                LEFT JOIN causalsentence cs ON cp.sentence_id = cs.sentence_id
                INNER JOIN pmid_counts pc ON (
                    cp.subject_cui = pc.subject_cui AND 
                    cp.object_cui = pc.object_cui AND 
                    cp.predicate = pc.predicate
                )
                WHERE (cp.subject_cui = ANY(%s) OR cp.object_cui = ANY(%s))
                AND cp.predicate = ANY(%s)
                
                UNION
                
                SELECT 
                    p.subject_cui, p.object_cui, p.predicate,
                    p.predication_id, p.pmid, p.sentence_id,
                    cs.sentence, p.subject_name, p.object_name,
                    cg.degree + 1,
                    cg.path || p.subject_cui
                FROM causalpred p
                LEFT JOIN causalsentence cs ON p.sentence_id = cs.sentence_id
                INNER JOIN pmid_counts pc ON (
                    p.subject_cui = pc.subject_cui AND 
                    p.object_cui = pc.object_cui AND 
                    p.predicate = pc.predicate
                )
                JOIN causal_graph cg ON p.subject_cui = cg.object_cui
                WHERE 
                    p.predicate = ANY(%s) 
                    AND cg.degree < %s
                    AND NOT p.subject_cui = ANY(cg.path)
            )
            SELECT DISTINCT * FROM causal_graph;
            """
            params = (
                pmid_threshold,  # For the HAVING clause
                exposure_cuis, outcome_cuis, 
                CAUSAL_PREDICATES,
                CAUSAL_PREDICATES, MAX_DEGREE
            )
        
        cur.execute(query, params)
        return [dict(zip([col.name for col in cur.description], row)) for row in cur.fetchall()]

def generate_dag_script(relationships: List[Dict], exposures: List[str], outcomes: List[str]) -> str:
    """Generate R script with dagitty DAG"""
    nodes = set()
    edges = []
    
    # Collect nodes and edges
    for rel in relationships:
        nodes.add((rel['subject_cui'], rel['subject_name']))
        nodes.add((rel['object_cui'], rel['object_name']))
        edges.append(f'"{rel["subject_cui"]}" -> "{rel["object_cui"]}" [label="{rel["predicate"]}"]')
    
    # Generate node attributes
    node_decls = []
    for cui, name in nodes:
        if cui in exposures:
            attr = f'[exposure, color=orange, label="{name}"]'
        elif cui in outcomes:
            attr = f'[outcome, color=blue, label="{name}"]'
        else:
            attr = f'[color=gray, label="{name}"]'
        node_decls.append(f'"{cui}" {attr}')
    
    # Build DAG string
    dag_content = "dag {\n" + "\n".join(node_decls) + "\n" + "\n".join(edges) + "\n}"
    
    return f"""library(dagitty)
# Generated DAG from CausalEHR database relationships
dag <- dagitty('{dag_content}')
"""

def generate_provenance(relationships: List[Dict]) -> Dict:
    """Generate JSON provenance structure with comprehensive sentence evidence"""
    from collections import defaultdict
    
    # Group relationships by edge to collect all supporting evidence
    edge_evidence = defaultdict(lambda: {
        'pmids': set(),
        'sentences': [],
        'predication_ids': set(),
        'sentence_ids': set()
    })
    
    edge_metadata = {}
    
    for rel in relationships:
        edge_key = (rel['subject_cui'], rel['object_cui'], rel['predicate'])
        
        # Store edge metadata (only need to do this once per edge)
        if edge_key not in edge_metadata:
            edge_metadata[edge_key] = {
                'from': rel['subject_cui'],
                'to': rel['object_cui'],
                'predicate': rel['predicate'],
                'subject_name': rel['subject_name'],
                'object_name': rel['object_name'],
                'degree': rel['degree']
            }
        
        # Collect evidence for this edge
        evidence = edge_evidence[edge_key]
        if rel['pmid']:
            evidence['pmids'].add(rel['pmid'])
        if rel['predication_id']:
            evidence['predication_ids'].add(rel['predication_id'])
        if rel['sentence_id']:
            evidence['sentence_ids'].add(rel['sentence_id'])
        if rel['sentence'] and rel['sentence'].strip():
            evidence['sentences'].append({
                'sentence_id': rel['sentence_id'],
                'pmid': rel['pmid'],
                'text': rel['sentence'].strip(),
                'predication_id': rel['predication_id']
            })
    
    # Build final edges with all supporting evidence
    edges = []
    for edge_key, metadata in edge_metadata.items():
        evidence = edge_evidence[edge_key]
        
        edge_data = {
            'from': metadata['from'],
            'to': metadata['to'],
            'predicate': metadata['predicate'],
            'subject_name': metadata['subject_name'],
            'object_name': metadata['object_name'],
            'degree': metadata['degree'],
            'evidence': {
                'pmids': sorted(list(evidence['pmids'])),
                'predication_ids': sorted(list(evidence['predication_ids'])),
                'sentence_ids': sorted(list(evidence['sentence_ids'])),
                'sentences': evidence['sentences'],
                'sentence_count': len(evidence['sentences']),
                'pmid_count': len(evidence['pmids'])
            }
        }
        edges.append(edge_data)
    
    # Sort edges by degree and then by subject/object for consistent output
    edges.sort(key=lambda x: (x['degree'], x['from'], x['to']))
    
    return {
        'edges': edges,
        'summary': {
            'total_edges': len(edges),
            'total_sentences': sum(len(e['evidence']['sentences']) for e in edges),
            'total_pmids': len(set().union(*[set(e['evidence']['pmids']) for e in edges])),
            'max_degree': max(e['degree'] for e in edges) if edges else 0
        }
    }

def main(exposure_cuis: List[str], outcome_cuis: List[str], pmid_threshold: int = 1):
    # Validate inputs
    for cui in exposure_cuis + outcome_cuis:
        if not validate_cui(cui):
            raise ValueError(f"Invalid CUI format: {cui}")
    
    conn = get_db_connection()
    try:
        print(f"Querying {MAX_DEGREE}-degree relationships...")
        relationships = query_causal_relationships(conn, exposure_cuis, outcome_cuis, pmid_threshold)
        
        print(f"Generating DAG script...")
        r_script = generate_dag_script(relationships, exposure_cuis, outcome_cuis)
        
        print("Generating provenance data...")
        provenance = generate_provenance(relationships)
        print(f"Found {provenance['summary']['total_edges']} unique causal relationships")
        print(f"Supported by {provenance['summary']['total_sentences']} sentences from {provenance['summary']['total_pmids']} PMIDs")
        print(f"Maximum relationship degree: {provenance['summary']['max_degree']}")
        
        # Write outputs
        with open('causal_dag.R', 'w') as f:
            f.write(r_script)
            
        with open('provenance.json', 'w') as f:
            json.dump(provenance, f, indent=2)
            
        print("Files written: causal_dag.R, provenance.json")
            
    finally:
        conn.close()

if __name__ == '__main__':
    # Example usage
    main(
        exposure_cuis=['C0011849'],  # Aspirin
        outcome_cuis=['C0026827'],   # Myocardial Infarction
        pmid_threshold=2
    )