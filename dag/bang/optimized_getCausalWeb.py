import psycopg2
import json
from typing import Dict, List, Set
from collections import defaultdict
import time

# Connection parameters (configure these for your environment)
CONN_PARAMS = {
    'host': 'localhost',
    'port': 5432,
    'dbname': 'causalehr',
    'user': 'r<PERSON><PERSON>',
    'password': 'Usps@6855',
    'options': '-c search_path=causalehr'
}

CAUSAL_PREDICATES = ['CAUSES', 'AFFECTS', 'TREATS', 'INHIBITS']
MAX_DEGREE = 3

def validate_cui(cui: str) -> bool:
    """Validate CUI format (C followed by 7 digits)"""
    return cui.startswith('C') and cui[1:].isdigit() and len(cui) == 8

def get_db_connection():
    """Establish database connection with error handling"""
    try:
        conn = psycopg2.connect(**CONN_PARAMS)
        # Enable query optimization
        with conn.cursor() as cur:
            cur.execute("SET work_mem = '256MB'")
            cur.execute("SET random_page_cost = 1.1")
            cur.execute("SET effective_cache_size = '1GB'")
        conn.commit()
        return conn
    except psycopg2.Error as e:
        raise RuntimeError(f"Database connection failed: {str(e)}")

def check_indexes(conn):
    """Check and suggest indexes for better performance"""
    with conn.cursor() as cur:
        # Check existing indexes
        cur.execute("""
            SELECT schemaname, tablename, indexname, indexdef 
            FROM pg_indexes 
            WHERE schemaname = 'causalehr' 
            AND tablename IN ('causalpred', 'causalsentence');
        """)
        indexes = cur.fetchall()
        
        print("Existing indexes:")
        for schema, table, idx_name, idx_def in indexes:
            print(f"  {table}: {idx_name}")
        
        # Suggest creating indexes if they don't exist
        suggested_indexes = [
            "CREATE INDEX IF NOT EXISTS idx_causalpred_subject_cui ON causalpred(subject_cui);",
            "CREATE INDEX IF NOT EXISTS idx_causalpred_object_cui ON causalpred(object_cui);",
            "CREATE INDEX IF NOT EXISTS idx_causalpred_predicate ON causalpred(predicate);",
            "CREATE INDEX IF NOT EXISTS idx_causalpred_composite ON causalpred(subject_cui, object_cui, predicate);",
            "CREATE INDEX IF NOT EXISTS idx_causalpred_pmid ON causalpred(pmid) WHERE pmid IS NOT NULL;",
            "CREATE INDEX IF NOT EXISTS idx_causalsentence_sentence_id ON causalsentence(sentence_id);"
        ]
        
        print("\nCreating recommended indexes (if they don't exist)...")
        for idx_sql in suggested_indexes:
            try:
                cur.execute(idx_sql)
                print(f"  ✓ {idx_sql.split()[-1]}")
            except Exception as e:
                print(f"  ✗ Index creation failed: {e}")
        
        conn.commit()

def query_causal_relationships_optimized(conn, exposure_cuis: List[str], outcome_cuis: List[str], pmid_threshold: int = 1) -> List[Dict]:
    """Optimized query with better performance"""
    
    with conn.cursor() as cur:
        # Use a more efficient approach - step by step instead of deep recursion
        start_time = time.time()
        
        # Step 1: Get high-confidence relationships only (with pmid filtering upfront)
        print("Step 1: Finding high-confidence direct relationships...")
        cur.execute("""
            CREATE TEMP TABLE IF NOT EXISTS temp_high_conf_rels AS
            SELECT subject_cui, object_cui, predicate, COUNT(DISTINCT pmid) as pmid_count
            FROM causalpred
            WHERE pmid IS NOT NULL 
            AND predicate = ANY(%s)
            GROUP BY subject_cui, object_cui, predicate
            HAVING COUNT(DISTINCT pmid) >= %s;
        """, (CAUSAL_PREDICATES, pmid_threshold))
        
        print(f"Step 1 completed in {time.time() - start_time:.2f}s")
        
        # Step 2: Get starting nodes (degree 1)
        step_time = time.time()
        print("Step 2: Finding degree-1 relationships...")
        cur.execute("""
            CREATE TEMP TABLE IF NOT EXISTS causal_paths AS
            SELECT 
                cp.subject_cui, cp.object_cui, cp.predicate, 
                cp.predication_id, cp.pmid, cp.sentence_id,
                cp.subject_name, cp.object_name,
                1 as degree,
                cp.subject_cui as path_start,
                cp.object_cui as path_end
            FROM causalpred cp
            INNER JOIN temp_high_conf_rels hc ON (
                cp.subject_cui = hc.subject_cui AND 
                cp.object_cui = hc.object_cui AND 
                cp.predicate = hc.predicate
            )
            WHERE (cp.subject_cui = ANY(%s) OR cp.object_cui = ANY(%s))
            AND cp.predicate = ANY(%s);
        """, (exposure_cuis, outcome_cuis, CAUSAL_PREDICATES))
        
        print(f"Step 2 completed in {time.time() - step_time:.2f}s")
        
        # Step 3: Extend paths iteratively (more efficient than deep recursion)
        for degree in range(2, MAX_DEGREE + 1):
            step_time = time.time()
            print(f"Step 3.{degree-1}: Finding degree-{degree} relationships...")
            
            cur.execute(f"""
                INSERT INTO causal_paths
                SELECT DISTINCT
                    p.subject_cui, p.object_cui, p.predicate,
                    p.predication_id, p.pmid, p.sentence_id,
                    p.subject_name, p.object_name,
                    %s as degree,
                    cp.path_start,
                    p.object_cui as path_end
                FROM causalpred p
                INNER JOIN temp_high_conf_rels hc ON (
                    p.subject_cui = hc.subject_cui AND 
                    p.object_cui = hc.object_cui AND 
                    p.predicate = hc.predicate
                )
                INNER JOIN causal_paths cp ON p.subject_cui = cp.object_cui
                WHERE cp.degree = %s
                AND p.predicate = ANY(%s)
                AND p.subject_cui != cp.path_start  -- Prevent cycles
                AND NOT EXISTS (
                    SELECT 1 FROM causal_paths existing 
                    WHERE existing.subject_cui = p.subject_cui 
                    AND existing.object_cui = p.object_cui 
                    AND existing.predicate = p.predicate
                );
            """, (degree, degree - 1, CAUSAL_PREDICATES))
            
            rows_added = cur.rowcount
            print(f"Step 3.{degree-1} completed in {time.time() - step_time:.2f}s - Added {rows_added} relationships")
            
            if rows_added == 0:
                print(f"No new relationships found at degree {degree}, stopping early.")
                break
        
        # Step 4: Get final results with sentence information
        step_time = time.time()
        print("Step 4: Joining with sentence data...")
        cur.execute("""
            SELECT DISTINCT
                cp.subject_cui, cp.object_cui, cp.predicate, 
                cp.predication_id, cp.pmid, cp.sentence_id,
                cs.sentence, cp.subject_name, cp.object_name,
                cp.degree
            FROM causal_paths cp
            LEFT JOIN causalsentence cs ON cp.sentence_id = cs.sentence_id
            ORDER BY cp.degree, cp.subject_cui, cp.object_cui;
        """)
        
        results = [dict(zip([col.name for col in cur.description], row)) for row in cur.fetchall()]
        print(f"Step 4 completed in {time.time() - step_time:.2f}s")
        
        # Cleanup temp tables
        cur.execute("DROP TABLE IF EXISTS temp_high_conf_rels, causal_paths;")
        
        total_time = time.time() - start_time
        print(f"Total query time: {total_time:.2f}s")
        
        return results

def generate_dag_script(relationships: List[Dict], exposures: List[str], outcomes: List[str]) -> str:
    """Generate R script with dagitty DAG - optimized for large datasets"""
    nodes = {}  # Use dict instead of set to avoid duplicates
    edges = set()  # Use set to avoid duplicate edges
    
    # Collect unique nodes and edges more efficiently
    for rel in relationships:
        nodes[rel['subject_cui']] = rel['subject_name']
        nodes[rel['object_cui']] = rel['object_name']
        edges.add((rel['subject_cui'], rel['object_cui'], rel['predicate']))
    
    # Generate node attributes
    node_decls = []
    for cui, name in nodes.items():
        if cui in exposures:
            attr = f'[exposure, color=orange, label="{name[:50]}"]'  # Truncate long names
        elif cui in outcomes:
            attr = f'[outcome, color=blue, label="{name[:50]}"]'
        else:
            attr = f'[color=gray, label="{name[:50]}"]'
        node_decls.append(f'"{cui}" {attr}')
    
    # Generate edges
    edge_decls = [f'"{subj}" -> "{obj}" [label="{pred}"]' for subj, obj, pred in edges]
    
    # Build DAG string
    dag_content = "dag {\n" + "\n".join(node_decls) + "\n" + "\n".join(edge_decls) + "\n}"
    
    return f"""library(dagitty)
library(ggdag)
# Generated DAG from CausalEHR database relationships
# Total nodes: {len(nodes)}, Total edges: {len(edges)}
dag <- dagitty('{dag_content}')

# Optional: Create a plot
# ggdag(dag) + theme_dag()
"""

def generate_provenance_optimized(relationships: List[Dict]) -> Dict:
    """Optimized provenance generation with better memory usage"""
    print("Generating provenance data...")
    start_time = time.time()
    
    # Use more efficient data structures
    edge_evidence = {}
    edge_metadata = {}
    all_pmids = set()
    
    # Single pass through relationships
    for i, rel in enumerate(relationships):
        if i % 1000 == 0 and i > 0:
            print(f"  Processed {i} relationships...")
            
        edge_key = (rel['subject_cui'], rel['object_cui'], rel['predicate'])
        
        # Initialize edge data if first time seeing this edge
        if edge_key not in edge_evidence:
            edge_evidence[edge_key] = {
                'pmids': set(),
                'sentences': [],
                'predication_ids': set(),
                'sentence_ids': set()
            }
            edge_metadata[edge_key] = {
                'from': rel['subject_cui'],
                'to': rel['object_cui'],
                'predicate': rel['predicate'],
                'subject_name': rel['subject_name'],
                'object_name': rel['object_name'],
                'degree': rel['degree']
            }
        
        # Collect evidence
        evidence = edge_evidence[edge_key]
        if rel['pmid']:
            evidence['pmids'].add(rel['pmid'])
            all_pmids.add(rel['pmid'])
        if rel['predication_id']:
            evidence['predication_ids'].add(rel['predication_id'])
        if rel['sentence_id']:
            evidence['sentence_ids'].add(rel['sentence_id'])
        if rel['sentence'] and rel['sentence'].strip():
            evidence['sentences'].append({
                'sentence_id': rel['sentence_id'],
                'pmid': rel['pmid'],
                'text': rel['sentence'].strip()[:500],  # Truncate long sentences
                'predication_id': rel['predication_id']
            })
    
    # Build final edges
    edges = []
    for edge_key, metadata in edge_metadata.items():
        evidence = edge_evidence[edge_key]
        
        edge_data = {
            'from': metadata['from'],
            'to': metadata['to'],
            'predicate': metadata['predicate'],
            'subject_name': metadata['subject_name'],
            'object_name': metadata['object_name'],
            'degree': metadata['degree'],
            'evidence': {
                'pmids': sorted(list(evidence['pmids'])),
                'sentence_count': len(evidence['sentences']),
                'pmid_count': len(evidence['pmids']),
                'predication_count': len(evidence['predication_ids'])
            }
        }
        
        # Only include first 10 sentences to keep file size manageable
        if evidence['sentences']:
            edge_data['evidence']['sample_sentences'] = evidence['sentences'][:10]
        
        edges.append(edge_data)
    
    # Sort edges
    edges.sort(key=lambda x: (x['degree'], x['from'], x['to']))
    
    total_time = time.time() - start_time
    print(f"Provenance generation completed in {total_time:.2f}s")
    
    return {
        'edges': edges,
        'summary': {
            'total_edges': len(edges),
            'total_sentences': sum(e['evidence']['sentence_count'] for e in edges),
            'total_pmids': len(all_pmids),
            'max_degree': max(e['degree'] for e in edges) if edges else 0,
            'generation_time_seconds': total_time
        }
    }

def main(exposure_cuis: List[str], outcome_cuis: List[str], pmid_threshold: int = 1):
    print(f"Starting causal relationship analysis...")
    print(f"Exposures: {exposure_cuis}")
    print(f"Outcomes: {outcome_cuis}")
    print(f"PMID threshold: {pmid_threshold}")
    print(f"Max degree: {MAX_DEGREE}")
    
    # Validate inputs
    for cui in exposure_cuis + outcome_cuis:
        if not validate_cui(cui):
            raise ValueError(f"Invalid CUI format: {cui}")
    
    conn = get_db_connection()
    try:
        # Check and create indexes for better performance
        check_indexes(conn)
        
        print(f"\nQuerying {MAX_DEGREE}-degree relationships...")
        start_time = time.time()
        relationships = query_causal_relationships_optimized(conn, exposure_cuis, outcome_cuis, pmid_threshold)
        query_time = time.time() - start_time
        
        print(f"\nQuery completed in {query_time:.2f}s")
        print(f"Found {len(relationships)} total relationship instances")
        
        if not relationships:
            print("No relationships found. Try lowering the pmid_threshold or checking your CUI values.")
            return
        
        print(f"\nGenerating DAG script...")
        r_script = generate_dag_script(relationships, exposure_cuis, outcome_cuis)
        
        provenance = generate_provenance_optimized(relationships)
        
        print(f"\nResults Summary:")
        print(f"- {provenance['summary']['total_edges']} unique causal relationships")
        print(f"- {provenance['summary']['total_sentences']} supporting sentences")
        print(f"- {provenance['summary']['total_pmids']} unique PMIDs")
        print(f"- Maximum relationship degree: {provenance['summary']['max_degree']}")
        
        # Write outputs
        print(f"\nWriting output files...")
        with open('causal_dag.R', 'w') as f:
            f.write(r_script)
            
        with open('provenance.json', 'w') as f:
            json.dump(provenance, f, indent=2)
        
        total_time = time.time() - start_time
        print(f"\nTotal execution time: {total_time:.2f}s")
        print("Files written: causal_dag.R, provenance.json")
            
    finally:
        conn.close()

if __name__ == '__main__':
    # Example usage
    main(
        exposure_cuis=['C0011849'],  # Aspirin
        outcome_cuis=['C0026827'],   # Myocardial Infarction
        pmid_threshold=2
    )